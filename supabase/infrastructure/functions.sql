-- Supabase Infrastructure: Database Functions
-- Contains stored procedures and functions for auction automation
-- This file contains ONLY function definitions - no table creation

-- Function to log notifications (for use by Edge Functions)
CREATE OR REPLACE FUNCTION public.log_notification(
    p_auction_id UUID,
    p_notification_type TEXT,
    p_recipient_email TEXT,
    p_recipient_type TEXT,
    p_status TEXT DEFAULT 'pending',
    p_message_id TEXT DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_processing_duration INTEGER DEFAULT NULL,
    p_notification_data JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO public.notification_log (
        auction_id,
        notification_type,
        recipient_email,
        recipient_type,
        status,
        message_id,
        error_message,
        processing_duration,
        notification_data,
        created_at,
        sent_at
    ) VALUES (
        p_auction_id,
        p_notification_type,
        p_recipient_email,
        p_recipient_type,
        p_status,
        p_message_id,
        p_error_message,
        p_processing_duration,
        p_notification_data,
        NOW(),
        CASE WHEN p_status = 'sent' THEN NOW() ELSE NULL END
    )
    RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$;

-- Grant execute permission on the logging function
GRANT EXECUTE ON FUNCTION public.log_notification TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_notification TO service_role;

-- Function to get notification statistics (for admin dashboard)
CREATE OR REPLACE FUNCTION public.get_notification_stats(
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
    notification_type TEXT,
    recipient_type TEXT,
    total_sent INTEGER,
    total_failed INTEGER,
    success_rate NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        nl.notification_type,
        nl.recipient_type,
        COUNT(CASE WHEN nl.status = 'sent' THEN 1 END)::INTEGER as total_sent,
        COUNT(CASE WHEN nl.status = 'failed' THEN 1 END)::INTEGER as total_failed,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(CASE WHEN nl.status = 'sent' THEN 1 END)::NUMERIC / COUNT(*)::NUMERIC) * 100, 2)
            ELSE 0
        END as success_rate
    FROM public.notification_log nl
    WHERE nl.created_at >= p_start_date 
      AND nl.created_at <= p_end_date
    GROUP BY nl.notification_type, nl.recipient_type
    ORDER BY nl.notification_type, nl.recipient_type;
END;
$$;

-- Grant execute permission on the stats function to admins
GRANT EXECUTE ON FUNCTION public.get_notification_stats TO authenticated;

-- Enhanced function to handle all auction notification events
CREATE OR REPLACE FUNCTION public.trigger_comprehensive_auction_notifications()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    closed_auction_ids UUID[];
    created_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- Get the Supabase project URL and service role key
    edge_function_url := current_setting('app.supabase_url', true) || '/functions/v1/sendAuctionNotification';
    service_role_key := current_setting('app.supabase_service_role_key', true);

    -- If the setting is not available, use a default pattern
    IF edge_function_url IS NULL OR edge_function_url = '/functions/v1/sendAuctionNotification' THEN
        edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';
    END IF;

    -- Find auctions that were created in the last 5 minutes (for admin notifications)
    SELECT ARRAY_AGG(id) INTO created_auction_ids
    FROM public.auction
    WHERE status = 'OPEN'::auction_state
      AND created_at >= NOW() - INTERVAL '5 minutes'
      AND created_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl
          WHERE nl.auction_id = auction.id
            AND nl.notification_type = 'ADMIN_AUCTION_CREATED'
            AND nl.status = 'sent'
      );

    -- Find auctions that were closed in the last 5 minutes
    SELECT ARRAY_AGG(id) INTO closed_auction_ids
    FROM public.auction
    WHERE status = 'CLOSED'::auction_state
      AND updated_at >= NOW() - INTERVAL '5 minutes'
      AND updated_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl
          WHERE nl.auction_id = auction.id
            AND nl.notification_type = 'AUCTION_CLOSED'
            AND nl.status = 'sent'
      );

    -- Process auction creation notifications (admin only)
    IF created_auction_ids IS NOT NULL AND array_length(created_auction_ids, 1) > 0 THEN
        -- Prepare request payload for auction creation
        request_payload := jsonb_build_object(
            'type', 'ADMIN_AUCTION_CREATED',
            'auctionIds', to_jsonb(created_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || service_role_key
                ),
                body := request_payload
            ) INTO response_id;
        EXCEPTION WHEN OTHERS THEN
            -- Log error but don't fail the entire process
            RAISE WARNING 'Failed to trigger auction creation notifications: %', SQLERRM;
        END;
    END IF;

    -- Process auction closure notifications (admin + account holder + winner selection)
    IF closed_auction_ids IS NOT NULL AND array_length(closed_auction_ids, 1) > 0 THEN
        -- Prepare request payload for auction closure
        request_payload := jsonb_build_object(
            'type', 'AUCTION_CLOSED',
            'auctionIds', to_jsonb(closed_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || service_role_key
                ),
                body := request_payload
            ) INTO response_id;
        EXCEPTION WHEN OTHERS THEN
            -- Log error but don't fail the entire process
            RAISE WARNING 'Failed to trigger auction closure notifications: %', SQLERRM;
        END;
    END IF;
END;
$$;

-- Manual trigger function for testing comprehensive auction notifications
CREATE OR REPLACE FUNCTION public.trigger_comprehensive_notifications_manual(
    auction_ids UUID[] DEFAULT NULL,
    notification_type TEXT DEFAULT 'auction_closed'
)
RETURNS TABLE(success BOOLEAN, message TEXT, auction_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    target_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- Use provided auction IDs or find recent auctions based on type
    IF auction_ids IS NOT NULL THEN
        target_auction_ids := auction_ids;
    ELSE
        IF notification_type = 'ADMIN_AUCTION_CREATED' THEN
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'OPEN'::auction_state
              AND created_at >= NOW() - INTERVAL '1 hour'
            LIMIT 5;
        ELSE
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'CLOSED'::auction_state
              AND updated_at >= NOW() - INTERVAL '1 hour'
            LIMIT 5;
        END IF;
    END IF;

    -- If no auctions found, return early
    IF target_auction_ids IS NULL OR array_length(target_auction_ids, 1) = 0 THEN
        RETURN QUERY SELECT false, 'No auctions found for notification type: ' || notification_type, 0;
        RETURN;
    END IF;

    -- Get configuration
    edge_function_url := current_setting('app.supabase_url', true) || '/functions/v1/sendAuctionNotification';
    service_role_key := current_setting('app.supabase_service_role_key', true);

    IF edge_function_url IS NULL OR edge_function_url = '/functions/v1/sendAuctionNotification' THEN
        edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';
    END IF;

    -- Prepare request payload
    request_payload := jsonb_build_object(
        'type', notification_type,
        'auctionIds', to_jsonb(target_auction_ids)
    );

    -- Make HTTP request
    BEGIN
        SELECT net.http_post(
            url := edge_function_url,
            headers := jsonb_build_object(
                'Content-Type', 'application/json',
                'Authorization', 'Bearer ' || service_role_key
            ),
            body := request_payload
        ) INTO response_id;

        RETURN QUERY SELECT true, 'Notifications triggered successfully', array_length(target_auction_ids, 1);

    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Failed to trigger notifications: ' || SQLERRM, array_length(target_auction_ids, 1);
    END;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.trigger_comprehensive_auction_notifications TO authenticated;
GRANT EXECUTE ON FUNCTION public.trigger_comprehensive_notifications_manual TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION public.log_notification IS
'Logs notification attempts with comprehensive metadata for monitoring and debugging. Used by Edge Functions to track email delivery status.';

COMMENT ON FUNCTION public.get_notification_stats IS
'Provides notification statistics for admin dashboard monitoring. Returns success rates and delivery metrics by notification and recipient type.';

COMMENT ON FUNCTION public.trigger_comprehensive_auction_notifications IS
'Enhanced cron job function that handles comprehensive auction lifecycle notifications including admin notifications for auction creation and closure, automatic winner selection, and system error handling.';

COMMENT ON FUNCTION public.trigger_comprehensive_notifications_manual IS
'Manual trigger function for testing comprehensive auction notifications. Supports both auction creation and closure notification types.';

-- Log successful function setup
DO $$
BEGIN
    RAISE NOTICE 'Supabase infrastructure functions created successfully: notification logging, statistics, and comprehensive auction notifications';
END $$;
